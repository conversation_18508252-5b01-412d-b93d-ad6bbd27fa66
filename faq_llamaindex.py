# financial_qa_generator_llamaindex.py
# 基于LlamaIndex生成高质量问答对，用于评估智能体问答准确率

import json
import logging
from typing import List, Dict, Any
from datetime import datetime
import pandas as pd

# Gemini SDK
import google.generativeai as genai

# OpenRouter for LLM
import openai

from dotenv import load_dotenv
# load env
load_dotenv()

# LlamaIndex 核心组件 (使用兼容版本)
try:
    from llama_index import (
        SimpleDirectoryReader,
        VectorStoreIndex,
        ServiceContext,
        Document,
        StorageContext
    )
    from llama_index.llms import OpenAI
    from llama_index.embeddings import BaseEmbedding
    from llama_index.vector_stores import MilvusVectorStore
    from llama_index.evaluation import DatasetGenerator
    from llama_index.question_gen import LLMQuestionGenerator
except ImportError:
    # 如果是新版本的LlamaIndex
    from llama_index.core import (
        SimpleDirectoryReader,
        VectorStoreIndex,
        ServiceContext,
        Document,
        StorageContext
    )
    from llama_index.llms.openai import OpenAI
    from llama_index.core.embeddings import BaseEmbedding
    from llama_index.vector_stores.milvus import MilvusVectorStore
    from llama_index.core.evaluation import DatasetGenerator
    from llama_index.core.question_gen import LLMQuestionGenerator

# Milvus SaaS 集成
from pymilvus import MilvusClient

# 配置文件
from config import Config

# 配置日志
config = Config()
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)


class GeminiEmbedding(BaseEmbedding):
    """Gemini Embedding 适配器，兼容LlamaIndex"""
    
    def __init__(self, api_key: str, model: str = "models/text-embedding-004"):
        super().__init__()
        genai.configure(api_key=api_key)
        self.model = model
        self.embed_batch_size = 10
    
    def _get_query_embedding(self, query: str) -> List[float]:
        """获取查询embedding"""
        return self._get_text_embedding(query)
    
    def _get_text_embedding(self, text: str) -> List[float]:
        """获取文本embedding"""
        # 检查token限制
        if not config.get_token_limit_for_text(text):
            text = config.truncate_text_to_token_limit(text)
            logger.warning(f"文本被截断以适应token限制: {len(text)} 字符")
        
        try:
            result = genai.embed_content(
                model=self.model,
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Gemini embedding 失败: {e}")
            # 返回零向量作为fallback
            return [0.0] * config.EMBEDDING_DIMENSION
    
    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        """批量获取文本embedding"""
        embeddings = []
        for i in range(0, len(texts), self.embed_batch_size):
            batch = texts[i:i + self.embed_batch_size]
            batch_embeddings = []
            for text in batch:
                embedding = self._get_text_embedding(text)
                batch_embeddings.append(embedding)
            embeddings.extend(batch_embeddings)
        return embeddings


class OpenRouterLLM:
    """OpenRouter LLM 适配器，兼容LlamaIndex"""
    
    def __init__(self, api_key: str, model: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.model = model
        self.temperature = 0.7
        self.max_tokens = 2000
    
    def complete(self, prompt: str) -> str:
        """生成文本补全"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            return response.choices[0].message.content or "无法生成回答"
        except Exception as e:
            logger.error(f"OpenRouter LLM 调用失败: {e}")
            return "抱歉，无法生成回答。"
    
    def predict(self, prompt: str) -> str:
        """LlamaIndex兼容方法"""
        return self.complete(prompt)


class FinancialQAGenerator:
    """财报问答生成器 - 基于LlamaIndex，用于生成评估数据集"""
    
    def __init__(self):
        """初始化配置"""
        # 验证配置
        if not config.validate_config():
            raise ValueError("配置验证失败，请检查环境变量")
        
        self.collection_name = config.MILVUS_COLLECTION_NAME
        
        # 配置 LLM 和 Embedding
        self.llm = OpenRouterLLM(
            api_key=config.OPENROUTER_API_KEY,
            model=config.OPENROUTER_MODEL,
            base_url=config.OPENROUTER_BASE_URL
        )
        
        self.embed_model = GeminiEmbedding(
            api_key=config.GEMINI_API_KEY,
            model=config.GEMINI_EMBEDDING_MODEL
        )
        
        # 初始化 Milvus 向量存储
        self.vector_store = MilvusVectorStore(
            uri=config.MILVUS_BASE_URL,
            token=config.MILVUS_TOKEN,
            collection_name=self.collection_name,
            dim=config.EMBEDDING_DIMENSION,
            overwrite=False
        )
        
        # 配置 LlamaIndex ServiceContext
        self.service_context = ServiceContext.from_defaults(
            llm=self.llm,
            embed_model=self.embed_model,
            chunk_size=config.CHUNK_SIZE,
            chunk_overlap=config.CHUNK_OVERLAP
        )
        
        logger.info(f"初始化完成，使用模型: {config.OPENROUTER_MODEL}")
        logger.info(f"Embedding模型: {config.GEMINI_EMBEDDING_MODEL}")
        logger.info(f"Milvus集合: {self.collection_name}")
    
    def load_financial_reports(self, reports_dir: str) -> List[Document]:
        """加载所有财报文档"""
        logger.info(f"开始加载财报目录: {reports_dir}")
        
        # 支持多种格式
        reader = SimpleDirectoryReader(
            input_dir=reports_dir,
            recursive=True,
            filename_as_id=True,
            required_exts=[".pdf", ".txt", ".docx", ".html", ".md"]
        )
        
        documents = reader.load_data()
        
        # 为每个文档添加元数据
        for doc in documents:
            # 从文件名提取公司名和年份
            filename = doc.doc_id or ""
            if "2023" in filename:
                doc.metadata["year"] = "2023"
            elif "2024" in filename:
                doc.metadata["year"] = "2024"
            
            # 提取公司名（示例逻辑）
            company_name = filename.split("_")[0] if "_" in filename else "未知公司"
            doc.metadata["company"] = company_name
            doc.metadata["doc_type"] = "financial_report"
        
        logger.info(f"成功加载 {len(documents)} 份财报")
        return documents
    
    def build_index(self, documents: List[Document]) -> VectorStoreIndex:
        """构建向量索引"""
        logger.info("开始构建向量索引...")
        
        # 创建存储上下文
        storage_context = StorageContext.from_defaults(
            vector_store=self.vector_store
        )
        
        # 构建索引
        index = VectorStoreIndex.from_documents(
            documents,
            service_context=self.service_context,
            storage_context=storage_context,
            show_progress=True
        )
        
        logger.info("向量索引构建完成")
        return index

    def generate_evaluation_qa_dataset(
        self,
        index: VectorStoreIndex,
        num_questions: int = 100
    ) -> List[Dict[str, Any]]:
        """生成用于评估的高质量问答数据集，包含跨公司对比分析"""
        logger.info(f"开始生成 {num_questions} 个评估问答对...")

        # 分析文档中的公司信息
        companies = self._extract_companies_from_index(index)
        logger.info(f"检测到的公司: {companies}")

        qa_pairs = []

        # 1. 生成单公司问题 (60%)
        single_company_questions = int(num_questions * 0.6)
        single_qa_pairs = self._generate_single_company_questions(index, single_company_questions)
        qa_pairs.extend(single_qa_pairs)

        # 2. 生成跨公司对比问题 (40%)
        if len(companies) >= 2:
            cross_company_questions = num_questions - len(single_qa_pairs)
            cross_qa_pairs = self._generate_cross_company_questions(index, companies, cross_company_questions)
            qa_pairs.extend(cross_qa_pairs)
        else:
            # 如果只有一家公司，继续生成单公司问题
            remaining_questions = num_questions - len(single_qa_pairs)
            additional_qa_pairs = self._generate_single_company_questions(index, remaining_questions)
            qa_pairs.extend(additional_qa_pairs)

        logger.info(f"成功生成 {len(qa_pairs)} 个评估问答对")
        return qa_pairs

    def _extract_companies_from_index(self, index: VectorStoreIndex) -> List[str]:
        """从索引中提取公司名称"""
        companies = set()
        for doc in index.docstore.docs.values():
            company = doc.metadata.get("company", "")
            if company and company != "未知公司":
                companies.add(company)
        return list(companies)

    def _generate_single_company_questions(self, index: VectorStoreIndex, num_questions: int) -> List[Dict[str, Any]]:
        """生成单公司问题"""
        logger.info(f"生成 {num_questions} 个单公司问题...")

        # 使用LlamaIndex的DatasetGenerator生成问答对
        dataset_generator = DatasetGenerator.from_documents(
            documents=list(index.docstore.docs.values()),
            service_context=self.service_context,
            num_questions_per_chunk=2,
            show_progress=True
        )

        # 生成问答对
        eval_questions = dataset_generator.generate_questions_from_nodes(num=num_questions)

        qa_pairs = []
        query_engine = index.as_query_engine(
            similarity_top_k=5,
            response_mode="tree_summarize",
            service_context=self.service_context
        )

        # 为每个问题生成高质量答案
        for i, question in enumerate(eval_questions):
            try:
                logger.info(f"生成单公司答案 {i+1}/{len(eval_questions)}: {question[:50]}...")

                # 使用查询引擎生成答案
                response = query_engine.query(question)

                # 提取源节点信息
                source_info = []
                if hasattr(response, 'source_nodes') and response.source_nodes:
                    for node in response.source_nodes:
                        source_info.append({
                            "company": node.node.metadata.get("company", "unknown"),
                            "year": node.node.metadata.get("year", "unknown"),
                            "score": getattr(node, 'score', 0.0)
                        })

                qa_pair = {
                    "question": question,
                    "answer": str(response),
                    "question_type": self._classify_question_type(question),
                    "difficulty": self._assess_question_difficulty(question),
                    "source_documents": source_info,
                    "generated_at": datetime.now().isoformat(),
                    "is_cross_company": False,  # 标记为单公司问题
                    "evaluation_metadata": {
                        "is_factual": self._is_factual_question(question),
                        "requires_calculation": self._requires_calculation(question),
                        "answer_length": len(str(response)),
                        "confidence_score": getattr(response, 'confidence', 0.0)
                    }
                }

                qa_pairs.append(qa_pair)

            except Exception as e:
                logger.error(f"生成单公司问答对失败: {e}")
                continue

        return qa_pairs

    def _generate_cross_company_questions(self, index: VectorStoreIndex, companies: List[str], num_questions: int) -> List[Dict[str, Any]]:
        """生成跨公司对比问题"""
        logger.info(f"生成 {num_questions} 个跨公司对比问题...")

        qa_pairs = []
        query_engine = index.as_query_engine(
            similarity_top_k=10,  # 增加检索数量以获取多公司信息
            response_mode="tree_summarize",
            service_context=self.service_context
        )

        # 定义跨公司对比问题模板
        comparison_templates = [
            # 营收对比
            "比较{company1}和{company2}的营业收入情况，哪家公司表现更好？",
            "分析{company1}、{company2}和{company3}的营收增长率，排名如何？",

            # 盈利能力对比
            "对比{company1}和{company2}的净利润率，分析差异原因",
            "比较{company1}和{company2}的毛利率水平，哪家更有竞争优势？",

            # 财务健康度对比
            "分析{company1}和{company2}的资产负债率，哪家财务更稳健？",
            "比较{company1}和{company2}的现金流状况，评估其流动性",

            # 成长性对比
            "对比{company1}和{company2}的业务增长潜力和发展前景",
            "分析{company1}和{company2}的研发投入占比，哪家更注重创新？",

            # 行业地位对比
            "比较{company1}和{company2}在行业中的市场地位和竞争优势",
            "分析{company1}、{company2}和{company3}的市场份额变化趋势"
        ]

        import random
        import itertools

        # 生成公司组合
        company_pairs = list(itertools.combinations(companies, 2))
        company_triplets = list(itertools.combinations(companies, 3)) if len(companies) >= 3 else []

        questions_generated = 0
        while questions_generated < num_questions and (company_pairs or company_triplets):
            try:
                # 随机选择模板
                template = random.choice(comparison_templates)

                # 根据模板需要的公司数量选择公司组合
                if "{company3}" in template and company_triplets:
                    company_combo = random.choice(company_triplets)
                    question = template.format(
                        company1=company_combo[0],
                        company2=company_combo[1],
                        company3=company_combo[2]
                    )
                    involved_companies = list(company_combo)
                elif company_pairs:
                    company_combo = random.choice(company_pairs)
                    question = template.format(
                        company1=company_combo[0],
                        company2=company_combo[1]
                    )
                    involved_companies = list(company_combo)
                else:
                    break

                logger.info(f"生成跨公司问题 {questions_generated+1}/{num_questions}: {question[:50]}...")

                # 使用查询引擎生成答案
                response = query_engine.query(question)

                # 提取源节点信息，确保包含多个公司的信息
                source_info = []
                companies_in_sources = set()
                if hasattr(response, 'source_nodes') and response.source_nodes:
                    for node in response.source_nodes:
                        company = node.node.metadata.get("company", "unknown")
                        companies_in_sources.add(company)
                        source_info.append({
                            "company": company,
                            "year": node.node.metadata.get("year", "unknown"),
                            "score": getattr(node, 'score', 0.0)
                        })

                # 检查是否真的包含了多个公司的信息
                if len(companies_in_sources) < 2:
                    logger.warning(f"跨公司问题的答案只包含 {len(companies_in_sources)} 个公司的信息，跳过")
                    continue

                qa_pair = {
                    "question": question,
                    "answer": str(response),
                    "question_type": "比较型",  # 跨公司问题都是比较型
                    "difficulty": "中等",  # 跨公司对比通常是中等难度
                    "source_documents": source_info,
                    "generated_at": datetime.now().isoformat(),
                    "is_cross_company": True,  # 标记为跨公司问题
                    "involved_companies": involved_companies,  # 记录涉及的公司
                    "evaluation_metadata": {
                        "is_factual": self._is_factual_question(question),
                        "requires_calculation": self._requires_calculation(question),
                        "answer_length": len(str(response)),
                        "confidence_score": getattr(response, 'confidence', 0.0),
                        "companies_in_answer": len(companies_in_sources)
                    }
                }

                qa_pairs.append(qa_pair)
                questions_generated += 1

            except Exception as e:
                logger.error(f"生成跨公司问答对失败: {e}")
                continue

        logger.info(f"成功生成 {len(qa_pairs)} 个跨公司对比问答对")
        return qa_pairs

    def _classify_question_type(self, question: str) -> str:
        """分类问题类型"""
        question_lower = question.lower()

        if any(word in question_lower for word in ["多少", "数量", "金额", "比例", "率"]):
            return "数值型"
        elif any(word in question_lower for word in ["为什么", "原因", "如何", "怎样"]):
            return "解释型"
        elif any(word in question_lower for word in ["比较", "对比", "差异", "区别"]):
            return "比较型"
        elif any(word in question_lower for word in ["趋势", "变化", "增长", "下降"]):
            return "趋势型"
        elif any(word in question_lower for word in ["风险", "挑战", "机遇", "前景"]):
            return "分析型"
        else:
            return "一般型"

    def _assess_question_difficulty(self, question: str) -> str:
        """评估问题难度"""
        # 简单的难度评估逻辑
        complexity_indicators = [
            "综合", "分析", "评估", "预测", "对比", "影响", "策略", "风险"
        ]

        complexity_count = sum(1 for indicator in complexity_indicators
                             if indicator in question)

        if complexity_count >= 3:
            return "困难"
        elif complexity_count >= 1:
            return "中等"
        else:
            return "简单"

    def _is_factual_question(self, question: str) -> bool:
        """判断是否为事实性问题"""
        factual_indicators = [
            "多少", "什么", "哪个", "何时", "数量", "金额", "比例"
        ]
        return any(indicator in question for indicator in factual_indicators)

    def _requires_calculation(self, question: str) -> bool:
        """判断是否需要计算"""
        calculation_indicators = [
            "增长率", "比例", "百分比", "倍数", "差额", "总计", "平均"
        ]
        return any(indicator in question for indicator in calculation_indicators)

    def save_evaluation_dataset(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """保存评估数据集"""
        logger.info(f"保存评估数据集到: {output_file}")

        # 保存为 JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qa_pairs, f, ensure_ascii=False, indent=2)

        # 生成统计报告
        self._generate_dataset_report(qa_pairs, output_file)

        # 保存为 Excel 便于查看
        self._save_as_excel(qa_pairs, output_file)

        logger.info(f"评估数据集已保存到 {output_file}")

    def _generate_dataset_report(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """生成数据集统计报告"""
        report = {
            "总问题数": len(qa_pairs),
            "问题类型分布": {},
            "难度分布": {},
            "事实性问题比例": 0,
            "需要计算的问题比例": 0,
            "平均答案长度": 0,
            "跨公司分析": {
                "跨公司问题数": 0,
                "单公司问题数": 0,
                "跨公司问题比例": 0,
                "涉及的公司": set(),
                "公司组合分析": {}
            },
            "生成时间": datetime.now().isoformat()
        }

        # 统计问题类型和跨公司分析
        for qa in qa_pairs:
            q_type = qa.get("question_type", "未知")
            report["问题类型分布"][q_type] = report["问题类型分布"].get(q_type, 0) + 1

            difficulty = qa.get("difficulty", "未知")
            report["难度分布"][difficulty] = report["难度分布"].get(difficulty, 0) + 1

            # 跨公司分析统计
            is_cross_company = qa.get("is_cross_company", False)
            if is_cross_company:
                report["跨公司分析"]["跨公司问题数"] += 1
                # 记录涉及的公司
                involved_companies = qa.get("involved_companies", [])
                for company in involved_companies:
                    report["跨公司分析"]["涉及的公司"].add(company)

                # 统计公司组合
                if len(involved_companies) >= 2:
                    combo_key = " vs ".join(sorted(involved_companies))
                    report["跨公司分析"]["公司组合分析"][combo_key] = \
                        report["跨公司分析"]["公司组合分析"].get(combo_key, 0) + 1
            else:
                report["跨公司分析"]["单公司问题数"] += 1

        # 统计其他指标
        factual_count = sum(1 for qa in qa_pairs
                           if qa.get("evaluation_metadata", {}).get("is_factual", False))
        calc_count = sum(1 for qa in qa_pairs
                        if qa.get("evaluation_metadata", {}).get("requires_calculation", False))

        report["事实性问题比例"] = factual_count / len(qa_pairs) if qa_pairs else 0
        report["需要计算的问题比例"] = calc_count / len(qa_pairs) if qa_pairs else 0

        # 平均答案长度
        total_length = sum(qa.get("evaluation_metadata", {}).get("answer_length", 0)
                          for qa in qa_pairs)
        report["平均答案长度"] = total_length / len(qa_pairs) if qa_pairs else 0

        # 完善跨公司分析统计
        cross_company_count = report["跨公司分析"]["跨公司问题数"]
        report["跨公司分析"]["跨公司问题比例"] = cross_company_count / len(qa_pairs) if qa_pairs else 0

        # 将set转换为list以便JSON序列化
        report["跨公司分析"]["涉及的公司"] = list(report["跨公司分析"]["涉及的公司"])

        # 保存报告
        report_file = output_file.replace('.json', '_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"数据集报告已保存到 {report_file}")

        # 打印关键统计信息
        logger.info(f"数据集统计摘要:")
        logger.info(f"  总问题数: {len(qa_pairs)}")
        logger.info(f"  跨公司问题: {cross_company_count} ({report['跨公司分析']['跨公司问题比例']:.1%})")
        logger.info(f"  单公司问题: {report['跨公司分析']['单公司问题数']}")
        logger.info(f"  涉及公司: {report['跨公司分析']['涉及的公司']}")
        if report["跨公司分析"]["公司组合分析"]:
            logger.info(f"  主要公司组合: {list(report['跨公司分析']['公司组合分析'].keys())[:3]}")

    def _save_as_excel(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """保存为Excel格式"""
        df_data = []
        for qa in qa_pairs:
            # 处理涉及的公司信息
            involved_companies = qa.get("involved_companies", [])
            companies_str = ", ".join(involved_companies) if involved_companies else ""

            df_data.append({
                "问题": qa["question"],
                "答案": qa["answer"],
                "问题类型": qa.get("question_type", ""),
                "难度": qa.get("difficulty", ""),
                "是否跨公司": qa.get("is_cross_company", False),
                "涉及公司": companies_str,
                "是否事实性": qa.get("evaluation_metadata", {}).get("is_factual", False),
                "需要计算": qa.get("evaluation_metadata", {}).get("requires_calculation", False),
                "答案长度": qa.get("evaluation_metadata", {}).get("answer_length", 0),
                "答案中公司数": qa.get("evaluation_metadata", {}).get("companies_in_answer", 1),
                "置信度": qa.get("evaluation_metadata", {}).get("confidence_score", 0),
                "生成时间": qa.get("generated_at", "")
            })

        df = pd.DataFrame(df_data)
        excel_file = output_file.replace('.json', '.xlsx')
        df.to_excel(excel_file, index=False)

        logger.info(f"Excel文件已保存到 {excel_file}")

    def run_evaluation_dataset_generation(
        self,
        reports_dir: str,
        output_file: str,
        num_questions: int = 100
    ) -> List[Dict[str, Any]]:
        """运行完整的评估数据集生成流程"""
        logger.info("="*60)
        logger.info("开始生成财报问答评估数据集")
        logger.info("="*60)

        try:
            # 1. 加载财报
            documents = self.load_financial_reports(reports_dir)
            if not documents:
                logger.error("没有找到任何财报文档")
                return []

            # 2. 构建索引
            index = self.build_index(documents)

            # 3. 生成评估问答对
            qa_pairs = self.generate_evaluation_qa_dataset(index, num_questions)

            # 4. 保存评估数据集
            if qa_pairs:
                self.save_evaluation_dataset(qa_pairs, output_file)
            else:
                logger.warning("没有生成任何问答对")

            logger.info("="*60)
            logger.info("评估数据集生成完成！")
            logger.info(f"共生成 {len(qa_pairs)} 个高质量问答对")
            logger.info("="*60)

            return qa_pairs

        except Exception as e:
            logger.error(f"生成过程中出现错误: {e}")
            return []


# 使用示例
if __name__ == "__main__":
    try:
        # 创建生成器实例
        generator = FinancialQAGenerator()

        # 运行评估数据集生成流程
        qa_pairs = generator.run_evaluation_dataset_generation(
            reports_dir="./financial_reports",  # 财报文件夹
            output_file="./evaluation_qa_dataset.json",  # 输出文件
            num_questions=100  # 生成问题数量
        )

        # 打印示例结果和统计信息
        if qa_pairs:
            print("\n=== 生成的评估问答对示例 ===")

            # 显示单公司问题示例
            single_company_qa = [qa for qa in qa_pairs if not qa.get('is_cross_company', False)]
            if single_company_qa:
                print("\n【单公司问题示例】")
                qa = single_company_qa[0]
                print(f"问题: {qa['question']}")
                print(f"答案: {qa['answer'][:200]}...")
                print(f"类型: {qa['question_type']} | 难度: {qa['difficulty']}")
                print("-" * 80)

            # 显示跨公司对比问题示例
            cross_company_qa = [qa for qa in qa_pairs if qa.get('is_cross_company', False)]
            if cross_company_qa:
                print("\n【跨公司对比问题示例】")
                for i, qa in enumerate(cross_company_qa[:2]):
                    print(f"\n跨公司问题 {i+1}: {qa['question']}")
                    print(f"答案: {qa['answer'][:200]}...")
                    print(f"涉及公司: {qa.get('involved_companies', [])}")
                    print(f"答案中公司数: {qa['evaluation_metadata'].get('companies_in_answer', 0)}")
                    print("-" * 80)

            # 统计信息
            print(f"\n=== 数据集统计 ===")
            print(f"总问题数: {len(qa_pairs)}")
            print(f"单公司问题: {len(single_company_qa)}")
            print(f"跨公司问题: {len(cross_company_qa)}")

            type_counts = {}
            difficulty_counts = {}
            companies_involved = set()

            for qa in qa_pairs:
                q_type = qa.get('question_type', '未知')
                difficulty = qa.get('difficulty', '未知')
                type_counts[q_type] = type_counts.get(q_type, 0) + 1
                difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1

                # 收集涉及的公司
                if qa.get('involved_companies'):
                    companies_involved.update(qa['involved_companies'])

            print("问题类型分布:", type_counts)
            print("难度分布:", difficulty_counts)
            print("涉及的公司:", list(companies_involved))

            if cross_company_qa:
                print(f"跨公司问题比例: {len(cross_company_qa)/len(qa_pairs):.1%}")

                # 显示公司组合统计
                combo_counts = {}
                for qa in cross_company_qa:
                    companies = qa.get('involved_companies', [])
                    if len(companies) >= 2:
                        combo_key = " vs ".join(sorted(companies))
                        combo_counts[combo_key] = combo_counts.get(combo_key, 0) + 1

                if combo_counts:
                    print("公司组合分布:", dict(list(combo_counts.items())[:5]))

        else:
            print("没有生成任何问答对，请检查配置和数据")

    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        print(f"错误: {e}")
        print("请检查:")
        print("1. 环境变量配置是否正确")
        print("2. API密钥是否有效")
        print("3. 财报文件夹是否存在")
        print("4. 依赖包是否正确安装")

# financial_qa_generator.py

import os
import json
import logging
from typing import List, Dict, <PERSON>ple
from datetime import datetime
import pandas as pd

# LlamaIndex 核心组件
from llama_index import (
    SimpleDirectoryReader,
    VectorStoreIndex,
    ServiceContext,
    Document,
    StorageContext
)
from llama_index.llms import OpenAI
from llama_index.embeddings import OpenAIEmbedding
from llama_index.node_parser import SimpleNodeParser
from llama_index.question_gen import LLMQuestionGenerator
from llama_index.evaluation import DatasetGenerator

# Milvus 集成 [[0]](#__0)
from llama_index.vector_stores import MilvusVectorStore
from pymilvus import connections, Collection, utility

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FinancialQAGenerator:
    """财报问答生成器"""
    
    def __init__(
        self,
        milvus_host: str = "localhost",
        milvus_port: int = 19530,
        collection_name: str = "financial_reports",
        openai_model: str = "gpt-3.5-turbo",
        embedding_model: str = "text-embedding-3-small"
    ):
        """初始化配置"""
        self.collection_name = collection_name
        self.openai_model = openai_model
        
        # 连接 Milvus [[1]](#__1)
        connections.connect(
            alias="default",
            host=milvus_host,
            port=milvus_port
        )
        
        # 配置 LLM 和 Embedding
        self.llm = OpenAI(
            model=openai_model,
            temperature=0.7,
            max_tokens=2000
        )
        
        self.embed_model = OpenAIEmbedding(
            model=embedding_model,
            embed_batch_size=100
        )
        
        # 配置服务上下文
        self.service_context = ServiceContext.from_defaults(
            llm=self.llm,
            embed_model=self.embed_model,
            chunk_size=512,  # 适合财报的块大小
            chunk_overlap=50
        )
        
        # 初始化 Milvus 向量存储 [[0]](#__0)
        self.vector_store = MilvusVectorStore(
            collection_name=collection_name,
            dim=1536,  # OpenAI embedding 维度
            overwrite=False,  # 保留已有数据
            consistency_level="Strong"
        )
        
        logger.info(f"初始化完成，使用模型: {openai_model}")
    
    def load_financial_reports(self, reports_dir: str) -> List[Document]:
        """加载所有财报文档"""
        logger.info(f"开始加载财报目录: {reports_dir}")
        
        # 支持多种格式
        reader = SimpleDirectoryReader(
            input_dir=reports_dir,
            recursive=True,
            filename_as_id=True,
            required_exts=[".pdf", ".txt", ".docx", ".html"]
        )
        
        documents = reader.load_data()
        
        # 为每个文档添加元数据
        for doc in documents:
            # 从文件名提取公司名和年份
            filename = doc.doc_id
            if "2023" in filename:
                doc.metadata["year"] = "2023"
            elif "2024" in filename:
                doc.metadata["year"] = "2024"
            
            # 提取公司名（示例逻辑）
            company_name = filename.split("_")[0] if "_" in filename else "未知公司"
            doc.metadata["company"] = company_name
            doc.metadata["doc_type"] = "financial_report"
        
        logger.info(f"成功加载 {len(documents)} 份财报")
        return documents
    
    def build_index(self, documents: List[Document]) -> VectorStoreIndex:
        """构建向量索引"""
        logger.info("开始构建向量索引...")
        
        # 创建存储上下文
        storage_context = StorageContext.from_defaults(
            vector_store=self.vector_store
        )
        
        # 构建索引 [[2]](#__2)
        index = VectorStoreIndex.from_documents(
            documents,
            service_context=self.service_context,
            storage_context=storage_context,
            show_progress=True
        )
        
        logger.info("向量索引构建完成")
        return index
    
    def generate_questions_with_answers(
        self,
        index: VectorStoreIndex,
        num_questions: int = 100
    ) -> List[Dict[str, str]]:
        """生成问答对"""
        logger.info(f"开始生成 {num_questions} 个问答对...")
        
        # 初始化问题生成器
        question_gen = LLMQuestionGenerator.from_defaults(
            service_context=self.service_context,
            prompt_template_str="""
            基于以下财报内容，生成一个专业的金融分析问题。
            问题应该：
            1. 具体且可以从数据中回答
            2. 涉及财务指标、趋势或对比
            3. 对投资者有价值
            
            上下文信息：
            {context_str}
            
            生成的问题：
            """
        )
        
        # 使用 DatasetGenerator 生成问答对 [[3]](#__3)
        dataset_generator = DatasetGenerator.from_documents(
            documents=index.docstore.docs.values(),
            service_context=self.service_context,
            num_questions_per_chunk=2,  # 每个块生成2个问题
            show_progress=True
        )
        
        # 分主题生成问题
        qa_pairs = []
        topics = [
            "营收和利润分析",
            "资产负债表分析",
            "现金流分析",
            "同比增长率",
            "行业对比",
            "风险因素",
            "未来展望"
        ]
        
        questions_per_topic = num_questions // len(topics)
        
        for topic in topics:
            logger.info(f"生成主题相关问题: {topic}")
            
            # 创建主题相关的查询引擎
            query_engine = index.as_query_engine(
                similarity_top_k=5,
                response_mode="tree_summarize",
                service_context=self.service_context
            )
            
            # 生成该主题的问题
            topic_questions = self._generate_topic_questions(
                query_engine,
                topic,
                questions_per_topic
            )
            
            # 为每个问题生成答案
            for question in topic_questions:
                try:
                    response = query_engine.query(question)
                    qa_pairs.append({
                        "question": question,
                        "answer": str(response),
                        "topic": topic,
                        "source_nodes": [node.node.metadata for node in response.source_nodes],
                        "confidence_score": response.metadata.get("score", 0.0)
                    })
                except Exception as e:
                    logger.error(f"生成答案失败: {e}")
                    continue
        
        logger.info(f"成功生成 {len(qa_pairs)} 个问答对")
        return qa_pairs
    
    def _generate_topic_questions(
        self,
        query_engine,
        topic: str,
        num_questions: int
    ) -> List[str]:
        """生成特定主题的问题"""
        prompt = f"""
        请生成{num_questions}个关于"{topic}"的财务分析问题。
        这些问题应该：
        1. 基于实际的财报数据
        2. 需要具体的数字或趋势分析
        3. 对投资决策有参考价值
        
        请直接列出问题，每行一个。
        """
        
        response = self.llm.complete(prompt)
        questions = [q.strip() for q in response.text.strip().split('\n') if q.strip()]
        
        return questions[:num_questions]
    
    def optimize_qa_pairs(self, qa_pairs: List[Dict]) -> List[Dict]:
        """优化和过滤问答对"""
        logger.info("开始优化问答对...")
        
        optimized_pairs = []
        
        for qa in qa_pairs:
            # 检查答案质量
            if len(qa["answer"]) < 50:  # 答案太短
                continue
            
            if "无法找到" in qa["answer"] or "没有相关信息" in qa["answer"]:
                continue
            
            # 优化答案格式
            optimized_answer = self._format_answer(qa["answer"])
            
            optimized_pairs.append({
                "question": qa["question"],
                "answer": optimized_answer,
                "topic": qa["topic"],
                "metadata": {
                    "sources": qa.get("source_nodes", []),
                    "confidence": qa.get("confidence_score", 0.0),
                    "generated_at": datetime.now().isoformat()
                }
            })
        
        logger.info(f"优化后保留 {len(optimized_pairs)} 个高质量问答对")
        return optimized_pairs
    
    def _format_answer(self, answer: str) -> str:
        """格式化答案"""
        # 使用 LLM 优化答案格式
        prompt = f"""
        请优化以下财务问题的答案，使其更加专业和结构化：
        
        原始答案：
        {answer}
        
        要求：
        1. 保留所有具体数据和事实
        2. 使用专业的财务术语
        3. 结构清晰，可以使用要点列举
        4. 如果有数据对比，请用表格或清晰的格式展示
        
        优化后的答案：
        """
        
        response = self.llm.complete(prompt)
        return response.text.strip()
    
    def save_qa_pairs(self, qa_pairs: List[Dict], output_file: str):
        """保存问答对到文件"""
        logger.info(f"保存问答对到: {output_file}")
        
        # 保存为 JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qa_pairs, f, ensure_ascii=False, indent=2)
        
        # 同时保存为 Excel 便于查看
        df_data = []
        for qa in qa_pairs:
            df_data.append({
                "问题": qa["question"],
                "答案": qa["answer"],
                "主题": qa["topic"],
                "置信度": qa["metadata"]["confidence"],
                "生成时间": qa["metadata"]["generated_at"]
            })
        
        df = pd.DataFrame(df_data)
        excel_file = output_file.replace('.json', '.xlsx')
        df.to_excel(excel_file, index=False)
        
        logger.info(f"问答对已保存到 {output_file} 和 {excel_file}")
    
    def run(self, reports_dir: str, output_file: str, num_questions: int = 100):
        """运行完整的问答生成流程"""
        logger.info("="*50)
        logger.info("开始财报问答生成流程")
        logger.info("="*50)
        
        # 1. 加载财报
        documents = self.load_financial_reports(reports_dir)
        
        # 2. 构建索引
        index = self.build_index(documents)
        
        # 3. 生成问答对
        qa_pairs = self.generate_questions_with_answers(index, num_questions)
        
        # 4. 优化问答对
        optimized_qa_pairs = self.optimize_qa_pairs(qa_pairs)
        
        # 5. 保存结果
        self.save_qa_pairs(optimized_qa_pairs, output_file)
        
        logger.info("="*50)
        logger.info("财报问答生成完成！")
        logger.info("="*50)
        
        return optimized_qa_pairs


# 使用示例
if __name__ == "__main__":
    # 配置参数
    config = {
        "reports_dir": "./financial_reports",  # 财报文件夹
        "output_file": "./qa_pairs.json",      # 输出文件
        "num_questions": 100,                  # 生成问题数量
        "milvus_host": "localhost",            # Milvus 地址
        "milvus_port": 19530,                  # Milvus 端口
        "collection_name": "financial_qa_v1",  # 集合名称
        "openai_model": "gpt-3.5-turbo",      # 使用的模型
    }
    
    # 设置 OpenAI API Key
    os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    
    # 创建生成器实例
    generator = FinancialQAGenerator(
        milvus_host=config["milvus_host"],
        milvus_port=config["milvus_port"],
        collection_name=config["collection_name"],
        openai_model=config["openai_model"]
    )
    
    # 运行生成流程
    qa_pairs = generator.run(
        reports_dir=config["reports_dir"],
        output_file=config["output_file"],
        num_questions=config["num_questions"]
    )
    
    # 打印示例结果
    print("\n生成的问答对示例：")
    for i, qa in enumerate(qa_pairs[:3]):
        print(f"\n问题 {i+1}: {qa['question']}")
        print(f"答案: {qa['answer'][:200]}...")
        print(f"主题: {qa['topic']}")
        print("-" * 80)

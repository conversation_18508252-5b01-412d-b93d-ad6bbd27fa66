# financial_qa_generator_improved.py

import os
import json
import logging
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime
import pandas as pd
import re
from pathlib import Path

# Milvus SaaS 集成
from pymilvus import MilvusClient, DataType

# Gemini SDK
import google.generativeai as genai

# OpenRouter for LLM
import openai

# 配置文件
from config import Config

# 配置日志
config = Config()
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)


class GeminiEmbedding:
    """Gemini Embedding 包装器"""
    
    def __init__(self, api_key: str, model: str = "models/text-embedding-004"):
        genai.configure(api_key=api_key)
        self.model = model
        self.embed_batch_size = 10  # Gemini API 批处理限制
    
    def get_text_embedding(self, text: str) -> List[float]:
        """获取单个文本的embedding"""
        # 检查token限制
        if not config.get_token_limit_for_text(text):
            text = config.truncate_text_to_token_limit(text)
            logger.warning(f"文本被截断以适应token限制: {len(text)} 字符")
        
        try:
            result = genai.embed_content(
                model=self.model,
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Gemini embedding 失败: {e}")
            # 返回零向量作为fallback
            return [0.0] * config.EMBEDDING_DIMENSION
    
    def get_text_embedding_batch(self, texts: List[str]) -> List[List[float]]:
        """批量获取embedding"""
        embeddings = []
        for i in range(0, len(texts), self.embed_batch_size):
            batch = texts[i:i + self.embed_batch_size]
            batch_embeddings = []
            for text in batch:
                embedding = self.get_text_embedding(text)
                batch_embeddings.append(embedding)
            embeddings.extend(batch_embeddings)
        return embeddings


class OpenRouterLLM:
    """OpenRouter LLM 包装器"""
    
    def __init__(self, api_key: str, model: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        self.model = model
        self.temperature = 0.7
        self.max_tokens = 2000
    
    def complete(self, prompt: str) -> str:
        """生成文本补全"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            return response.choices[0].message.content or "无法生成回答"
        except Exception as e:
            logger.error(f"OpenRouter LLM 调用失败: {e}")
            return "抱歉，无法生成回答。"
    
    def chat(self, messages: List[Dict[str, str]]) -> str:
        """聊天模式"""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            return response.choices[0].message.content or "无法生成回答"
        except Exception as e:
            logger.error(f"OpenRouter LLM 聊天失败: {e}")
            return "抱歉，无法生成回答。"


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, chunk_size: int = 400, chunk_overlap: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def load_documents(self, reports_dir: str) -> List[Dict[str, Any]]:
        """加载所有财报文档"""
        logger.info(f"开始加载财报目录: {reports_dir}")
        
        documents = []
        reports_path = Path(reports_dir)
        
        if not reports_path.exists():
            logger.error(f"目录不存在: {reports_dir}")
            return documents
        
        # 支持的文件格式
        supported_extensions = ['.txt', '.md', '.pdf', '.docx']
        
        for file_path in reports_path.rglob('*'):
            if file_path.suffix.lower() in supported_extensions:
                try:
                    content = self._read_file(file_path)
                    if content:
                        # 提取元数据
                        metadata = self._extract_metadata(file_path)
                        
                        documents.append({
                            'content': content,
                            'metadata': metadata,
                            'file_path': str(file_path)
                        })
                        logger.info(f"成功加载文件: {file_path.name}")
                except Exception as e:
                    logger.error(f"加载文件失败 {file_path}: {e}")
        
        logger.info(f"成功加载 {len(documents)} 份文档")
        return documents
    
    def _read_file(self, file_path: Path) -> str:
        """读取文件内容"""
        try:
            if file_path.suffix.lower() == '.txt' or file_path.suffix.lower() == '.md':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            elif file_path.suffix.lower() == '.pdf':
                # 这里需要安装 PyPDF2 或 pdfplumber
                logger.warning(f"PDF文件需要额外的库支持: {file_path}")
                return ""
            elif file_path.suffix.lower() == '.docx':
                # 这里需要安装 python-docx
                logger.warning(f"DOCX文件需要额外的库支持: {file_path}")
                return ""
            else:
                return ""
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            return ""
    
    def _extract_metadata(self, file_path: Path) -> Dict[str, str]:
        """从文件名提取元数据"""
        filename = file_path.stem
        metadata = {
            'filename': filename,
            'file_type': 'financial_report',
            'year': 'unknown',
            'company': 'unknown'
        }
        
        # 提取年份
        year_match = re.search(r'20\d{2}', filename)
        if year_match:
            metadata['year'] = year_match.group()
        
        # 提取公司名（假设文件名格式为 "公司名_年份_其他信息"）
        parts = filename.split('_')
        if len(parts) > 0:
            metadata['company'] = parts[0]
        
        return metadata
    
    def chunk_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将文档分块"""
        chunks = []
        
        for doc in documents:
            content = doc['content']
            metadata = doc['metadata']
            
            # 简单的文本分块
            text_chunks = self._split_text(content)
            
            for i, chunk_text in enumerate(text_chunks):
                chunk = {
                    'content': chunk_text,
                    'metadata': {
                        **metadata,
                        'chunk_id': i,
                        'total_chunks': len(text_chunks)
                    }
                }
                chunks.append(chunk)
        
        logger.info(f"文档分块完成，共 {len(chunks)} 个块")
        return chunks
    
    def _split_text(self, text: str) -> List[str]:
        """简单的文本分块"""
        # 按句子分割
        sentences = re.split(r'[.!?。！？]', text)
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 检查添加这个句子是否会超过chunk_size
            if len(current_chunk) + len(sentence) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        # 添加最后一个chunk
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks


class FinancialQAGenerator:
    """财报问答生成器 - 改进版"""

    def __init__(self):
        """初始化配置"""
        # 验证配置
        if not config.validate_config():
            raise ValueError("配置验证失败，请检查环境变量")

        self.collection_name = config.MILVUS_COLLECTION_NAME

        # 初始化 Milvus SaaS 客户端
        self.milvus_client = MilvusClient(
            uri=config.MILVUS_BASE_URL,
            token=config.MILVUS_TOKEN
        )

        # 配置 LLM 和 Embedding
        self.llm = OpenRouterLLM(
            api_key=config.OPENROUTER_API_KEY,
            model=config.OPENROUTER_MODEL,
            base_url=config.OPENROUTER_BASE_URL
        )

        self.embed_model = GeminiEmbedding(
            api_key=config.GEMINI_API_KEY,
            model=config.GEMINI_EMBEDDING_MODEL
        )

        # 文档处理器
        self.doc_processor = DocumentProcessor(
            chunk_size=config.CHUNK_SIZE,
            chunk_overlap=config.CHUNK_OVERLAP
        )

        # 初始化向量数据库
        self._init_collection()

        logger.info(f"初始化完成，使用模型: {config.OPENROUTER_MODEL}")
        logger.info(f"Embedding模型: {config.GEMINI_EMBEDDING_MODEL}")
        logger.info(f"Milvus集合: {self.collection_name}")

    def _init_collection(self):
        """初始化Milvus集合"""
        try:
            # 检查集合是否存在
            if self.milvus_client.has_collection(self.collection_name):
                logger.info(f"集合 {self.collection_name} 已存在")
                return

            # 创建集合schema
            schema = self.milvus_client.create_schema(
                auto_id=True,
                enable_dynamic_field=True
            )

            # 添加字段
            schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
            schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=config.EMBEDDING_DIMENSION)
            schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=2000)
            schema.add_field(field_name="company", datatype=DataType.VARCHAR, max_length=100)
            schema.add_field(field_name="year", datatype=DataType.VARCHAR, max_length=10)

            # 创建集合
            self.milvus_client.create_collection(
                collection_name=self.collection_name,
                schema=schema
            )

            # 创建索引
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128}
            }

            self.milvus_client.create_index(
                collection_name=self.collection_name,
                field_name="vector",
                index_params=index_params
            )

            logger.info(f"成功创建集合: {self.collection_name}")

        except Exception as e:
            logger.error(f"初始化集合失败: {e}")
            raise

    def load_and_index_documents(self, reports_dir: str):
        """加载文档并建立索引"""
        logger.info("开始加载和索引文档...")

        # 1. 加载文档
        documents = self.doc_processor.load_documents(reports_dir)
        if not documents:
            logger.error("没有找到任何文档")
            return

        # 2. 分块
        chunks = self.doc_processor.chunk_documents(documents)

        # 3. 生成embeddings并插入向量数据库
        self._insert_chunks_to_milvus(chunks)

        logger.info("文档加载和索引完成")

    def _insert_chunks_to_milvus(self, chunks: List[Dict[str, Any]]):
        """将文档块插入Milvus"""
        logger.info(f"开始插入 {len(chunks)} 个文档块到Milvus...")

        batch_size = 100
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]

            # 准备数据
            texts = [chunk['content'] for chunk in batch]
            embeddings = self.embed_model.get_text_embedding_batch(texts)

            # 准备插入数据
            data = []
            for j, chunk in enumerate(batch):
                data.append({
                    "vector": embeddings[j],
                    "content": chunk['content'][:2000],  # 限制长度
                    "company": chunk['metadata'].get('company', 'unknown'),
                    "year": chunk['metadata'].get('year', 'unknown')
                })

            # 插入数据
            try:
                self.milvus_client.insert(
                    collection_name=self.collection_name,
                    data=data
                )
                logger.info(f"成功插入批次 {i//batch_size + 1}/{(len(chunks)-1)//batch_size + 1}")
            except Exception as e:
                logger.error(f"插入批次失败: {e}")

        logger.info("所有文档块插入完成")

    def search_similar_content(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似内容"""
        try:
            # 生成查询向量
            query_vector = self.embed_model.get_text_embedding(query)

            # 搜索
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
            results = self.milvus_client.search(
                collection_name=self.collection_name,
                data=[query_vector],
                anns_field="vector",
                search_params=search_params,
                limit=top_k,
                output_fields=["content", "company", "year"]
            )

            # 格式化结果
            formatted_results = []
            for hit in results[0]:
                formatted_results.append({
                    "content": hit["entity"]["content"],
                    "company": hit["entity"]["company"],
                    "year": hit["entity"]["year"],
                    "score": hit["score"]
                })

            return formatted_results

        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []

    def generate_qa_pairs(self, num_questions: int = 100) -> List[Dict[str, Any]]:
        """生成问答对"""
        logger.info(f"开始生成 {num_questions} 个问答对...")

        # 定义财务分析主题
        topics = [
            "营收和利润分析",
            "资产负债表分析",
            "现金流分析",
            "同比增长率",
            "行业对比",
            "风险因素",
            "未来展望"
        ]

        qa_pairs = []
        questions_per_topic = max(1, num_questions // len(topics))

        for topic in topics:
            logger.info(f"生成主题相关问题: {topic}")

            # 为每个主题生成问题
            topic_questions = self._generate_topic_questions(topic, questions_per_topic)

            # 为每个问题生成答案
            for question in topic_questions:
                try:
                    answer = self._generate_answer(question)
                    if answer and len(answer) > 50:  # 过滤太短的答案
                        qa_pairs.append({
                            "question": question,
                            "answer": answer,
                            "topic": topic,
                            "generated_at": datetime.now().isoformat()
                        })
                except Exception as e:
                    logger.error(f"生成答案失败: {e}")
                    continue

        logger.info(f"成功生成 {len(qa_pairs)} 个问答对")
        return qa_pairs

    def _generate_topic_questions(self, topic: str, num_questions: int) -> List[str]:
        """生成特定主题的问题"""
        prompt = f"""
        请生成{num_questions}个关于"{topic}"的财务分析问题。
        这些问题应该：
        1. 基于实际的财报数据
        2. 需要具体的数字或趋势分析
        3. 对投资决策有参考价值
        4. 适合中国A股上市公司

        请直接列出问题，每行一个，不要编号。
        """

        try:
            response = self.llm.complete(prompt)
            questions = [q.strip() for q in response.split('\n') if q.strip() and not q.strip().isdigit()]
            return questions[:num_questions]
        except Exception as e:
            logger.error(f"生成主题问题失败: {e}")
            return []

    def _generate_answer(self, question: str) -> str:
        """为问题生成答案"""
        try:
            # 1. 搜索相关内容
            relevant_content = self.search_similar_content(question, top_k=3)

            if not relevant_content:
                return "抱歉，没有找到相关的财报信息来回答这个问题。"

            # 2. 构建上下文
            context = "\n\n".join([
                f"来源: {item['company']} ({item['year']})\n内容: {item['content']}"
                for item in relevant_content
            ])

            # 3. 生成答案
            prompt = f"""
            基于以下财报信息，请回答问题。要求：
            1. 答案要专业、准确
            2. 引用具体的数据和事实
            3. 如果信息不足，请明确说明
            4. 使用中文回答

            问题: {question}

            相关财报信息:
            {context}

            答案:
            """

            answer = self.llm.complete(prompt)
            return answer

        except Exception as e:
            logger.error(f"生成答案失败: {e}")
            return "抱歉，生成答案时出现错误。"

    def save_qa_pairs(self, qa_pairs: List[Dict[str, Any]], output_file: str):
        """保存问答对到文件"""
        logger.info(f"保存问答对到: {output_file}")

        # 保存为 JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(qa_pairs, f, ensure_ascii=False, indent=2)

        # 同时保存为 Excel 便于查看
        df_data = []
        for qa in qa_pairs:
            df_data.append({
                "问题": qa["question"],
                "答案": qa["answer"],
                "主题": qa["topic"],
                "生成时间": qa["generated_at"]
            })

        df = pd.DataFrame(df_data)
        excel_file = output_file.replace('.json', '.xlsx')
        df.to_excel(excel_file, index=False)

        logger.info(f"问答对已保存到 {output_file} 和 {excel_file}")

    def run(self, reports_dir: str, output_file: str, num_questions: int = 100):
        """运行完整的问答生成流程"""
        logger.info("="*50)
        logger.info("开始财报问答生成流程 - 改进版")
        logger.info("="*50)

        try:
            # 1. 加载财报并建立索引
            self.load_and_index_documents(reports_dir)

            # 2. 生成问答对
            qa_pairs = self.generate_qa_pairs(num_questions)

            # 3. 保存结果
            if qa_pairs:
                self.save_qa_pairs(qa_pairs, output_file)
            else:
                logger.warning("没有生成任何问答对")

            logger.info("="*50)
            logger.info("财报问答生成完成！")
            logger.info("="*50)

            return qa_pairs

        except Exception as e:
            logger.error(f"运行过程中出现错误: {e}")
            return []


# 使用示例
if __name__ == "__main__":
    try:
        # 创建生成器实例
        generator = FinancialQAGenerator()

        # 运行生成流程
        qa_pairs = generator.run(
            reports_dir="./financial_reports",  # 财报文件夹
            output_file="./qa_pairs_improved.json",  # 输出文件
            num_questions=50  # 生成问题数量
        )

        # 打印示例结果
        if qa_pairs:
            print("\n生成的问答对示例：")
            for i, qa in enumerate(qa_pairs[:3]):
                print(f"\n问题 {i+1}: {qa['question']}")
                print(f"答案: {qa['answer'][:200]}...")
                print(f"主题: {qa['topic']}")
                print("-" * 80)
        else:
            print("没有生成任何问答对，请检查配置和数据")

    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        print(f"错误: {e}")
        print("请检查:")
        print("1. 环境变量配置是否正确")
        print("2. API密钥是否有效")
        print("3. 财报文件夹是否存在")
        print("4. 网络连接是否正常")

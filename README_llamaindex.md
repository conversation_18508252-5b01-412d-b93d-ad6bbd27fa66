# 财报问答评估数据集生成器

基于 LlamaIndex + Milvus SaaS + Gemini + OpenRouter 的高质量问答数据集生成系统，专门用于评估智能体的问答准确率。

## 🎯 项目目标

生成高质量的财报问答对，用于：
- **智能体评估**: 测试AI助手在财务分析方面的准确性
- **基准测试**: 建立财务问答的标准评估数据集
- **模型对比**: 比较不同AI模型在财务领域的表现

## 🏗️ 技术架构

- **文档处理**: LlamaIndex 自动文档解析和分块
- **向量数据库**: Milvus SaaS 版本，云端部署
- **Embedding**: Google Gemini text-embedding-004 (768维)
- **大语言模型**: OpenRouter 平台的多种模型
- **问答生成**: LlamaIndex DatasetGenerator 高质量问答对生成

## 📊 生成的数据集特点

### 问答对结构
```json
{
  "question": "问题内容",
  "answer": "详细答案",
  "question_type": "数值型/解释型/比较型/趋势型/分析型",
  "difficulty": "简单/中等/困难",
  "source_documents": [
    {
      "company": "公司名",
      "year": "年份",
      "score": "相关性分数"
    }
  ],
  "evaluation_metadata": {
    "is_factual": true/false,
    "requires_calculation": true/false,
    "answer_length": 答案字符数,
    "confidence_score": 置信度分数
  },
  "generated_at": "生成时间"
}
```

### 问题类型分类
1. **数值型**: 涉及具体数字、金额、比例的问题
2. **解释型**: 需要解释原因、方法的问题
3. **比较型**: 需要对比分析的问题
4. **趋势型**: 涉及变化趋势的问题
5. **分析型**: 需要深度分析的问题

### 难度评估
- **简单**: 直接从文档中可以找到答案
- **中等**: 需要一定的理解和推理
- **困难**: 需要综合分析和深度思考

## 🚀 快速开始

### 1. 环境配置

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥
```

### 2. 准备数据

创建财报文件夹：
```
financial_reports/
├── 公司A_2023年报.txt
├── 公司B_2024年报.md
├── 公司C_2023年报.pdf
└── ...
```

支持格式：`.txt`, `.md`, `.pdf`, `.docx`, `.html`

### 3. 运行生成

```bash
python faq_llamaindex.py
```

或者自定义参数：
```python
from faq_llamaindex import FinancialQAGenerator

generator = FinancialQAGenerator()
qa_pairs = generator.run_evaluation_dataset_generation(
    reports_dir="./financial_reports",
    output_file="./evaluation_dataset.json",
    num_questions=200
)
```

## 📋 输出文件

运行后会生成以下文件：

1. **evaluation_dataset.json**: 完整的问答数据集
2. **evaluation_dataset_report.json**: 数据集统计报告
3. **evaluation_dataset.xlsx**: Excel格式，便于查看和编辑

### 统计报告示例
```json
{
  "总问题数": 100,
  "问题类型分布": {
    "数值型": 25,
    "解释型": 20,
    "比较型": 15,
    "趋势型": 20,
    "分析型": 20
  },
  "难度分布": {
    "简单": 30,
    "中等": 50,
    "困难": 20
  },
  "事实性问题比例": 0.6,
  "需要计算的问题比例": 0.3,
  "平均答案长度": 150
}
```

## 🔧 配置说明

### API 密钥配置

在 `.env` 文件中配置：

```env
# Milvus SaaS
MILVUS_BASE_URL=https://your-cluster.milvus.io
MILVUS_TOKEN=your_token

# Gemini API
GEMINI_API_KEY=your_gemini_key

# OpenRouter
OPENROUTER_API_KEY=your_openrouter_key
OPENROUTER_MODEL=anthropic/claude-3-sonnet
```

### 参数调优

在 `config.py` 中可以调整：

```python
CHUNK_SIZE = 400  # 文档分块大小
CHUNK_OVERLAP = 50  # 分块重叠
EMBEDDING_DIMENSION = 768  # Gemini embedding维度
GEMINI_MAX_TOKENS = 2048  # Gemini token限制
```

## 📈 评估使用

生成的数据集可以用于：

### 1. 智能体准确率评估
```python
def evaluate_agent(agent, qa_dataset):
    correct = 0
    for qa in qa_dataset:
        agent_answer = agent.answer(qa['question'])
        if is_correct(agent_answer, qa['answer']):
            correct += 1
    return correct / len(qa_dataset)
```

### 2. 不同难度级别评估
```python
def evaluate_by_difficulty(agent, qa_dataset):
    results = {}
    for difficulty in ['简单', '中等', '困难']:
        subset = [qa for qa in qa_dataset if qa['difficulty'] == difficulty]
        results[difficulty] = evaluate_agent(agent, subset)
    return results
```

### 3. 问题类型评估
```python
def evaluate_by_type(agent, qa_dataset):
    results = {}
    for q_type in ['数值型', '解释型', '比较型', '趋势型', '分析型']:
        subset = [qa for qa in qa_dataset if qa['question_type'] == q_type]
        results[q_type] = evaluate_agent(agent, subset)
    return results
```

## 🔍 质量保证

### 自动质量检查
- 答案长度过滤（最少50字符）
- 相关性分数阈值
- 重复问题检测
- 答案完整性验证

### 人工质量审核
生成的Excel文件便于人工审核和编辑：
- 标记低质量问答对
- 修正答案内容
- 调整难度分类
- 补充元数据

## 🚨 注意事项

1. **Token限制**: Gemini模型有2048 token限制，系统会自动截断
2. **API配额**: 注意各API的调用限制和费用
3. **数据质量**: 财报文档质量直接影响生成的问答质量
4. **网络稳定**: 需要稳定的网络连接访问各种API

## 🛠️ 故障排除

### 常见问题

1. **LlamaIndex版本兼容性**
   ```bash
   pip install llama-index==0.9.48  # 使用稳定版本
   ```

2. **Milvus连接失败**
   - 检查Base URL格式
   - 确认Token有效性
   - 验证网络连接

3. **Gemini API限制**
   - 检查API密钥
   - 确认配额充足
   - 调整请求频率

4. **内存不足**
   - 减少batch_size
   - 降低num_questions
   - 分批处理文档

## 📚 扩展功能

### 自定义问题模板
```python
def custom_question_generator(context, topic):
    templates = {
        "营收分析": "根据{company}的财报，{year}年的营收情况如何？",
        "风险评估": "{company}在{year}年面临的主要风险有哪些？"
    }
    return templates[topic].format(**context)
```

### 多语言支持
- 英文财报处理
- 多语言问答生成
- 跨语言评估对比

### 行业特化
- 银行业专用模板
- 制造业指标分析
- 科技公司评估标准

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

特别欢迎：
- 新的问题类型模板
- 质量评估算法改进
- 多语言支持
- 行业特化功能

"""
配置文件 - 管理API密钥和系统设置
"""
import os
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """系统配置类"""
    
    # Milvus SaaS 配置
    MILVUS_BASE_URL: str = os.getenv("MILVUS_BASE_URL", "")
    MILVUS_TOKEN: str = os.getenv("MILVUS_TOKEN", "")
    MILVUS_COLLECTION_NAME: str = os.getenv("MILVUS_COLLECTION_NAME", "financial_qa_v2")
    
    # Gemini API 配置
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    GEMINI_EMBEDDING_MODEL: str = "models/text-embedding-004"
    GEMINI_MAX_TOKENS: int = 2048
    GEMINI_CHARS_PER_TOKEN: int = 4  # 约4个字符/令牌
    
    # OpenRouter 配置
    OPENROUTER_API_KEY: str = os.getenv("OPENROUTER_API_KEY", "")
    OPENROUTER_BASE_URL: str = "https://openrouter.ai/api/v1"
    OPENROUTER_MODEL: str = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3-sonnet")
    
    # 系统设置
    CHUNK_SIZE: int = 400  # 适应Gemini token限制
    CHUNK_OVERLAP: int = 50
    EMBEDDING_DIMENSION: int = 768  # Gemini text-embedding-004 维度
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        required_configs = [
            ("MILVUS_BASE_URL", cls.MILVUS_BASE_URL),
            ("MILVUS_TOKEN", cls.MILVUS_TOKEN),
            ("GEMINI_API_KEY", cls.GEMINI_API_KEY),
            ("OPENROUTER_API_KEY", cls.OPENROUTER_API_KEY),
        ]
        
        missing_configs = []
        for name, value in required_configs:
            if not value:
                missing_configs.append(name)
        
        if missing_configs:
            print(f"缺少必要配置: {', '.join(missing_configs)}")
            print("请在环境变量或.env文件中设置这些配置")
            return False
        
        return True
    
    @classmethod
    def get_token_limit_for_text(cls, text: str) -> bool:
        """检查文本是否超过Gemini token限制"""
        estimated_tokens = len(text) // cls.GEMINI_CHARS_PER_TOKEN
        return estimated_tokens <= cls.GEMINI_MAX_TOKENS
    
    @classmethod
    def truncate_text_to_token_limit(cls, text: str) -> str:
        """截断文本以适应token限制"""
        max_chars = cls.GEMINI_MAX_TOKENS * cls.GEMINI_CHARS_PER_TOKEN
        if len(text) <= max_chars:
            return text
        return text[:max_chars] + "..."


# 创建全局配置实例
config = Config()

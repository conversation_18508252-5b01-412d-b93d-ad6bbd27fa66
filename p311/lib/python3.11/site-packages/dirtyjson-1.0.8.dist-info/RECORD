dirtyjson-1.0.8.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
dirtyjson-1.0.8.dist-info/LICENSE.txt,sha256=57r-tCLdR67jLwDLtliC7_LbHtfOk6J0_HAU3FB5TEs,10376
dirtyjson-1.0.8.dist-info/METADATA,sha256=dhrE6sxngOU_NEiyr8QPL69JmH1iSa-iWiD77YmEjk4,11587
dirtyjson-1.0.8.dist-info/RECORD,,
dirtyjson-1.0.8.dist-info/WHEEL,sha256=2wepM1nk4DS4eFpYrW1TTqPcoGNfHhhO_i5m4cOimbo,92
dirtyjson-1.0.8.dist-info/top_level.txt,sha256=RhTlMx3IFex5QCYcS7kfnvgRyBKWPmQvwWZS0twXJ7Q,10
dirtyjson-1.0.8.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
dirtyjson/__init__.py,sha256=kw5sZv0QlChWGaKQ940Ja-WqMAcFvtw58Zf3jk4cENA,4095
dirtyjson/__pycache__/__init__.cpython-311.pyc,,
dirtyjson/__pycache__/attributed_containers.cpython-311.pyc,,
dirtyjson/__pycache__/compat.cpython-311.pyc,,
dirtyjson/__pycache__/error.cpython-311.pyc,,
dirtyjson/__pycache__/loader.cpython-311.pyc,,
dirtyjson/attributed_containers.py,sha256=g62iOBEvlLCOBr1GFk2RTUFxQD9UhIWx_UyHIZReRdQ,4439
dirtyjson/compat.py,sha256=wdMuNqFXnRwBqB0ulOb58srR7UetR6jTXAo-KVzk_RI,1260
dirtyjson/error.py,sha256=KesgKmUtzeiZOJQpBo6FU2GoU29BKOv96pji1zMV7B4,1231
dirtyjson/loader.py,sha256=EzEaLI2_iDVqAFsYck_e8wai7N8RTfWn7uyoQqKyz6Q,14479
dirtyjson/tests/__init__.py,sha256=FUsr-_-meLvuBE9OIF_J8y8ttlZckJQJjDxtL0daw_0,1261
dirtyjson/tests/__pycache__/__init__.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_decimal.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_decode.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_errors.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_fail.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_float.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_integer.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_pass1.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_pass2.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_pass3.cpython-311.pyc,,
dirtyjson/tests/__pycache__/test_unicode.cpython-311.pyc,,
dirtyjson/tests/test_decimal.py,sha256=z9QJy0pWW2Dfuc3OAsqwoTJ-j_HuZWUIC7wHzFLbpEU,523
dirtyjson/tests/test_decode.py,sha256=shTOe9f5kn3qJIuFk1GoatJGOMsvLe5Yg1GeGRM4azI,4561
dirtyjson/tests/test_errors.py,sha256=JJpnSLlXy0lTCcsQmjvl9xBWyB6p5DqAFxIkDLINCWA,481
dirtyjson/tests/test_fail.py,sha256=oEsFEXGL0zJYgraKkc1dkWCLD_08zBLziHSDjEOlsHg,4763
dirtyjson/tests/test_float.py,sha256=1cF1v3hy33W2FqV8Kdao_1AiNHAgKEFo4u_SywNz_Gk,988
dirtyjson/tests/test_integer.py,sha256=hp1E5-dLZ23Qc7CbuB-DaDEhpf9gYrm-kSU6Z8Cjg3c,496
dirtyjson/tests/test_pass1.py,sha256=Y0QNBhQLSkI7zHj97S_U3nnOclwelevUleFpJ_j8_j4,1759
dirtyjson/tests/test_pass2.py,sha256=XVAM8UhnogaYxfKupRHbo0d6QZCyUO2TUmzH_PHZemo,400
dirtyjson/tests/test_pass3.py,sha256=_FZ8mrBa6ZEndnUeQldURcwkEKxbXjEdczyZymmhRRo,536
dirtyjson/tests/test_unicode.py,sha256=W2KunyH7Q3Xwxy-9o81IMeDE_KwdDf-SeFoLZ7u9iZE,2367

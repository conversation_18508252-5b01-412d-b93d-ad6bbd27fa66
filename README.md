# 财报问答生成器 - 改进版

基于 Milvus SaaS、Gemini Embedding 和 OpenRouter 的财报问答系统。

## 技术栈

- **向量数据库**: Milvus SaaS 版
- **Embedding模型**: Google Gemini API (models/text-embedding-004)
- **大语言模型**: OpenRouter 平台的各种模型
- **文档处理**: 自定义文档处理器
- **编程语言**: Python 3.8+

## 主要特性

1. **Milvus SaaS 集成**: 使用云端向量数据库，无需本地部署
2. **Gemini Embedding**: 使用 Google 最新的 embedding 模型
3. **Token 限制管理**: 自动处理 Gemini 2048 token 限制
4. **OpenRouter 集成**: 支持多种大语言模型
5. **智能文档分块**: 适应 token 限制的文档处理
6. **多格式支持**: 支持 TXT、MD、PDF、DOCX 等格式

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置环境变量

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入你的 API 密钥：

```env
# Milvus SaaS 配置
MILVUS_BASE_URL=https://your-cluster.milvus.io
MILVUS_TOKEN=your_milvus_token_here
MILVUS_COLLECTION_NAME=financial_qa_v2

# Gemini API 配置
GEMINI_API_KEY=your_gemini_api_key_here

# OpenRouter 配置
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=anthropic/claude-3-sonnet

# 可选配置
LOG_LEVEL=INFO
```

## 获取 API 密钥

### 1. Milvus SaaS
- 访问 [Milvus Cloud](https://cloud.milvus.io/)
- 创建集群并获取连接信息
- 获取 Base URL 和 Token

### 2. Gemini API
- 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
- 创建 API 密钥

### 3. OpenRouter
- 访问 [OpenRouter](https://openrouter.ai/)
- 注册账户并获取 API 密钥
- 选择合适的模型（推荐 Claude 3 Sonnet）

## 使用方法

### 1. 准备财报数据

创建 `financial_reports` 文件夹，放入财报文件：

```
financial_reports/
├── 公司A_2023年报.txt
├── 公司B_2024年报.md
└── 公司C_2023年报.pdf
```

### 2. 运行程序

```bash
python faq_improved.py
```

### 3. 自定义使用

```python
from faq_improved import FinancialQAGenerator

# 创建生成器
generator = FinancialQAGenerator()

# 加载文档并建立索引
generator.load_and_index_documents("./financial_reports")

# 生成问答对
qa_pairs = generator.generate_qa_pairs(num_questions=100)

# 保存结果
generator.save_qa_pairs(qa_pairs, "output.json")
```

## 配置说明

### Token 限制
- Gemini 模型限制：2048 tokens
- 约 4 个字符 = 1 token
- 系统会自动截断超长文本

### 文档分块
- 默认块大小：400 字符
- 重叠大小：50 字符
- 可在 `config.py` 中调整

### 向量维度
- Gemini text-embedding-004：768 维
- 自动配置，无需手动设置

## 输出格式

生成的问答对包含以下字段：

```json
{
  "question": "问题内容",
  "answer": "答案内容", 
  "topic": "主题分类",
  "generated_at": "生成时间"
}
```

同时会生成 Excel 文件便于查看和编辑。

## 主题分类

系统会自动生成以下主题的问题：

1. 营收和利润分析
2. 资产负债表分析
3. 现金流分析
4. 同比增长率
5. 行业对比
6. 风险因素
7. 未来展望

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查 `.env` 文件是否存在
   - 确认所有必要的环境变量都已设置

2. **Milvus 连接失败**
   - 检查 Base URL 格式是否正确
   - 确认 Token 是否有效
   - 检查网络连接

3. **Gemini API 调用失败**
   - 确认 API 密钥是否有效
   - 检查是否超出配额限制
   - 确认网络可以访问 Google API

4. **OpenRouter 调用失败**
   - 检查 API 密钥是否正确
   - 确认账户余额是否充足
   - 检查选择的模型是否可用

### 日志调试

设置日志级别为 DEBUG：

```env
LOG_LEVEL=DEBUG
```

## 性能优化

1. **批处理**: Embedding 生成使用批处理，提高效率
2. **缓存**: 向量数据库提供持久化存储
3. **并发**: 可以考虑添加异步处理提高速度

## 扩展功能

系统设计为模块化，可以轻松扩展：

1. 添加新的文档格式支持
2. 集成其他 Embedding 模型
3. 支持更多 LLM 提供商
4. 添加问答质量评估
5. 实现增量更新机制

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
